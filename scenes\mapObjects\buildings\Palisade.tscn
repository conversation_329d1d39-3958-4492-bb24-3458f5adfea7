[gd_scene load_steps=8 format=3 uid="uid://c72jn0sno32k5"]

[ext_resource type="Script" uid="uid://c0l8sbdvsy48t" path="res://scenes/mapObjects/buildings/Palisade.cs" id="1_script"]
[ext_resource type="Texture2D" uid="uid://bpcmwrhn34d2s" path="res://resources/pixel mood/palisades/palisadeLeftRight.png" id="2_texture"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_progressbar"]

[sub_resource type="Animation" id="Animation_reset"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_hit"]
resource_name = "hit"
length = 0.2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 0.42, 0.27, 1), Color(1, 1, 1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
&"RESET": SubResource("Animation_reset"),
&"hit": SubResource("Animation_hit")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(16, 32)

[node name="Palisade" type="Node2D"]
script = ExtResource("1_script")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_texture")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_1")
}

[node name="StaticBody2D" type="StaticBody2D" parent="."]
collision_layer = 2
collision_mask = 0

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
shape = SubResource("RectangleShape2D_1")

[node name="ProgressBar" parent="." instance=ExtResource("3_progressbar")]
show_behind_parent = true
