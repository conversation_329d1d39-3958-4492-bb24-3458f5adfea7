using Godot;
using System;
using System.Collections.Generic;

public partial class Palisade : Node2D, IDestroyableObject, ICombatTarget
{
	[Export] public ObjectType BuildingType { get; set; } = ObjectType.Palisade;
	[Export] public int MaxHealth { get; set; } = 15;

	private const int BUILDING_WIDTH = 1;
	private const int BUILDING_HEIGHT = 2;
	private const int TILE_SIZE = 16;
	private const int MAX_HEALTH = 15;

	private int _currentHealth;
	private Vector2I _topLeftTilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isPlaced = false;
	private bool _isBeingDestroyed = false;
	private string _saveId = "";

	private Sprite2D _sprite;
	private StaticBody2D _staticBody;
	private CollisionShape2D _collisionShape;
	private ProgressBar _hpBar;
	private AnimationPlayer _animationPlayer;

	private Color _normalColor = Colors.White;
	private Color _hitColor = new Color(1.0f, 0.42f, 0.27f, 1.0f);
	private float _hitTintStrength = 0.8f;
	private float _hitAnimationDuration = 0.2f;
	private Tween _hitTween;

	private Dictionary<string, Texture2D> _palisadeTextures = new Dictionary<string, Texture2D>();

	public override void _Ready()
	{
		_currentHealth = MAX_HEALTH;

		_sprite = GetNode<Sprite2D>("Sprite2D");
		_staticBody = GetNode<StaticBody2D>("StaticBody2D");
		_collisionShape = GetNode<CollisionShape2D>("StaticBody2D/CollisionShape2D");
		_hpBar = GetNode<ProgressBar>("ProgressBar");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");

		if (_hpBar != null)
		{
			_hpBar.SetProgress(1.0f);
			_hpBar.Visible = false;
		}

		LoadPalisadeTextures();
		SetTilePosition(_topLeftTilePosition);

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
			CommonSignals.Instance.HammerUsed += OnHammerUsed;
			CommonSignals.Instance.SwordUsed += OnSwordUsed;
		}

		UpdateHPBar();
	}

	private void LoadPalisadeTextures()
	{
		_palisadeTextures["LeftRight"] = GD.Load<Texture2D>("res://resources/pixel mood/palisades/palisadeLeftRight.png");
		_palisadeTextures["TopDown"] = GD.Load<Texture2D>("res://resources/pixel mood/palisades/palisadeTopDownt.png");
		_palisadeTextures["DownLeft"] = GD.Load<Texture2D>("res://resources/pixel mood/palisades/palisadeDownLeft.png");
		_palisadeTextures["DownRight"] = GD.Load<Texture2D>("res://resources/pixel mood/palisades/palisadeDownRight.png");
		_palisadeTextures["UpLeft"] = GD.Load<Texture2D>("res://resources/pixel mood/palisades/palisadeUpLeft.png");
		_palisadeTextures["UpRight"] = GD.Load<Texture2D>("res://resources/pixel mood/palisades/palisadeUpRight.png");

		// New complex connection textures
		_palisadeTextures["LeftRightDown"] = GD.Load<Texture2D>("res://resources/pixel mood/palisades/palisadeLeftRightDown.png");
		_palisadeTextures["LeftRightTopDown"] = GD.Load<Texture2D>("res://resources/pixel mood/palisades/palisadeLeftRightTopDown.png");
		_palisadeTextures["LeftTopDown"] = GD.Load<Texture2D>("res://resources/pixel mood/palisades/palisadeLeftTopDown.png");
		_palisadeTextures["RightTopDown"] = GD.Load<Texture2D>("res://resources/pixel mood/palisades/palisadeRightTopDown.png");
		_palisadeTextures["LeftRightTop"] = GD.Load<Texture2D>("res://resources/pixel mood/palisades/palisadeLeftRightTop.png");

		if (_sprite != null && _palisadeTextures.ContainsKey("LeftRight"))
		{
			_sprite.Texture = _palisadeTextures["LeftRight"];
		}
	}

	public void LoadFromSave(Vector2I topLeftPosition, CustomDataLayerManager customDataManager, int health)
	{
		_topLeftTilePosition = topLeftPosition;
		_customDataManager = customDataManager;
		_currentHealth = health;
		_isPlaced = true;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = new Vector2I(topLeftPosition.X + x, topLeftPosition.Y + y);
				customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}

		SetTilePosition(topLeftPosition);
		UpdateHPBar();
		UpdatePalisadeTexture();
	}

	public void SetSaveId(string saveId)
	{
		_saveId = saveId;
	}

	public void PlaceBuilding()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		if (!resourcesManager.HasResource(ResourceType.WoodenBeam, 5))
		{
			return;
		}

		if (!resourcesManager.RemoveResource(ResourceType.WoodenBeam, 5))
		{
			return;
		}

		PlaceBuilding(_topLeftTilePosition, _customDataManager);
	}

	public void PlaceBuilding(Vector2I topLeftPosition, CustomDataLayerManager customDataManager)
	{
		_topLeftTilePosition = topLeftPosition;
		_customDataManager = customDataManager;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = new Vector2I(topLeftPosition.X + x, topLeftPosition.Y + y);
				customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}

		SetTilePosition(topLeftPosition);

		_isPlaced = true;

		_saveId = ResourcesManager.Instance?.AddBuilding(_topLeftTilePosition, "Palisade", (int)BuildingType);

		UpdatePalisadeTexture();
		NotifyAdjacentPalisades();
	}

	public Vector2I GetTopLeftTilePosition()
	{
		return _topLeftTilePosition;
	}

	public void SetTilePosition(Vector2I topLeftTile)
	{
		_topLeftTilePosition = topLeftTile;
		Vector2 worldPosition = new Vector2(
			topLeftTile.X * TILE_SIZE + (BUILDING_WIDTH * TILE_SIZE) / 2.0f,
			topLeftTile.Y * TILE_SIZE + (BUILDING_HEIGHT * TILE_SIZE) / 2.0f
		);
		GlobalPosition = worldPosition;
	}

	public bool CanBePlacedAt(Vector2I topLeftPosition)
	{
		if (_customDataManager == null)
		{
			_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		}

		if (_customDataManager == null) return false;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = topLeftPosition + new Vector2I(x, y);
				var tileData = _customDataManager.GetTileData(tilePos);

				if (!tileData.CanBuilding || tileData.ObjectTypePlaced != ObjectTypePlaced.None)
				{
					return false;
				}
			}
		}

		return true;
	}

	public void SetPlacementFeedback(bool canPlace)
	{
		if (_sprite != null)
		{
			if (canPlace)
			{
				_sprite.Modulate = Colors.White;
			}
			else
			{
				_sprite.Modulate = new Color(1.0f, 0.5f, 0.5f, 0.8f);
			}
		}
	}

	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;

		UpdateHPBar();
		SaveBuildingState();

		PlayHitAnimation();

		if (_animationPlayer != null && _animationPlayer.HasAnimation("hit"))
		{
			_animationPlayer.Play("hit");
		}

		if (_currentHealth <= 0)
		{
			DestroyBuilding();
		}
	}

	private void PlayHitAnimation()
	{
		if (_sprite == null) return;

		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.SetParallel(true);

		var hitColor = _normalColor.Lerp(_hitColor, _hitTintStrength);
		_hitTween.TweenProperty(_sprite, "modulate", hitColor, _hitAnimationDuration * 0.1f);
		_hitTween.TweenProperty(_sprite, "modulate", _normalColor, _hitAnimationDuration * 0.9f)
				.SetDelay(_hitAnimationDuration * 0.1f);
	}

	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		float progress = (float)_currentHealth / MAX_HEALTH;
		_hpBar.SetProgress(progress);
		_hpBar.Visible = _currentHealth < MAX_HEALTH && _currentHealth > 0;
	}

	private void SaveBuildingState()
	{
		if (!_isPlaced || string.IsNullOrEmpty(_saveId)) return;

		var buildingData = GameSaveData.Instance.WorldData.Buildings.Find(b => b.Id == _saveId);
		if (buildingData != null)
		{
			buildingData.CurrentHealth = _currentHealth;
		}
	}

	private void DestroyBuilding()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
				_customDataManager?.ClearObjectPlaced(tilePos);
			}
		}

		if (!string.IsNullOrEmpty(_saveId))
		{
			var buildingData = GameSaveData.Instance.WorldData.Buildings.Find(b => b.Id == _saveId);
			if (buildingData != null)
			{
				GameSaveData.Instance.WorldData.Buildings.Remove(buildingData);
			}
		}

		NotifyAdjacentPalisades();

		QueueFree();
		GD.Print("Palisade: Building destroyed");
	}

	private void UpdatePalisadeTexture()
	{
		if (_sprite == null || !_isPlaced) return;

		string textureKey = DetermineTextureDirection();
		if (_palisadeTextures.ContainsKey(textureKey))
		{
			_sprite.Texture = _palisadeTextures[textureKey];
		}
	}

	private string DetermineTextureDirection()
	{
		if (_customDataManager == null) return "LeftRight";

		// Since palisades are 1x2 (height=2), we need to check connections properly
		// Check for palisades to the left and right (horizontal connections)
		bool hasLeft = HasPalisadeAt(_topLeftTilePosition + Vector2I.Left);
		bool hasRight = HasPalisadeAt(_topLeftTilePosition + Vector2I.Right);

		// Check for palisades above and below (vertical connections)
		// Since our palisade occupies 2 tiles vertically, we need to check:
		// - 2 tiles up (to connect with palisade above)
		// - 2 tiles down (to connect with palisade below)
		bool hasUp = HasPalisadeAt(_topLeftTilePosition + new Vector2I(0, -2));
		bool hasDown = HasPalisadeAt(_topLeftTilePosition + new Vector2I(0, 2));

		// Determine texture based on connections - prioritize complex connections first
		// 4-way connection
		if (hasLeft && hasRight && hasUp && hasDown) return "LeftRightTopDown";

		// 3-way connections
		if (hasLeft && hasRight && hasDown) return "LeftRightDown";
		if (hasLeft && hasRight && hasUp) return "LeftRightTop";
		if (hasLeft && hasUp && hasDown) return "LeftTopDown";
		if (hasRight && hasUp && hasDown) return "RightTopDown";

		// 2-way connections (corners and straight lines)
		if (hasLeft && hasRight) return "LeftRight";
		if (hasUp && hasDown) return "TopDown";
		if (hasDown && hasLeft) return "DownLeft";
		if (hasDown && hasRight) return "DownRight";
		if (hasUp && hasLeft) return "UpLeft";
		if (hasUp && hasRight) return "UpRight";

		// Single connections (fallback to basic shapes)
		if (hasLeft || hasRight) return "LeftRight";
		if (hasUp || hasDown) return "TopDown";

		return "LeftRight";
	}

	private bool HasPalisadeAt(Vector2I position)
	{
		if (_customDataManager == null) return false;

		var tileData = _customDataManager.GetTileData(position);
		if (tileData.ObjectTypePlaced != ObjectTypePlaced.Building) return false;

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode == null) return false;

		foreach (Node child in buildingsNode.GetChildren())
		{
			if (child is Palisade palisade && palisade._isPlaced)
			{
				var palisadePos = palisade.GetTopLeftTilePosition();
				if (palisadePos == position)
				{
					return true;
				}
			}
		}

		return false;
	}

	private void NotifyAdjacentPalisades()
	{
		if (_customDataManager == null) return;

		// Since palisades are 1x2, we need to check all positions where palisades could connect
		Vector2I[] adjacentPositions = {
			// Horizontal connections (left and right)
			_topLeftTilePosition + Vector2I.Left,
			_topLeftTilePosition + Vector2I.Right,
			// Vertical connections (2 tiles up and 2 tiles down since palisades are 2 tiles high)
			_topLeftTilePosition + new Vector2I(0, -2),
			_topLeftTilePosition + new Vector2I(0, 2)
		};

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode == null) return;

		foreach (Vector2I pos in adjacentPositions)
		{
			foreach (Node child in buildingsNode.GetChildren())
			{
				if (child is Palisade palisade && palisade._isPlaced)
				{
					var palisadePos = palisade.GetTopLeftTilePosition();
					if (palisadePos == pos)
					{
						palisade.UpdatePalisadeTexture();
					}
				}
			}
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I occupiedTile = _topLeftTilePosition + new Vector2I(x, y);
				if (occupiedTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeHitFrom(playerTile))
					{
						TakeDamage(damage);
					}
					return;
				}
			}
		}
	}

	private void OnHammerUsed(Vector2I tilePosition, int repairAmount)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I buildingTile = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
				if (buildingTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeRepairedFrom(playerTile))
					{
						RepairBuilding(repairAmount);
					}
					return;
				}
			}
		}
	}

	private void OnSwordUsed(Vector2I tilePosition, Vector2 playerPosition, Vector2 attackDirection)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I occupiedTile = _topLeftTilePosition + new Vector2I(x, y);
				if (occupiedTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeHitFrom(playerTile))
					{
						TakeDamage(3);
					}
					return;
				}
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			Vector2 playerPos = player.GlobalPosition;
			return new Vector2I((int)playerPos.X / 16, (int)playerPos.Y / 16);
		}
		return Vector2I.Zero;
	}

	private bool CanBeRepairedFrom(Vector2I playerTilePosition)
	{
		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I buildingTile = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
				float distance = buildingTile.DistanceTo(playerTilePosition);
				if (distance <= 2.0f)
				{
					return true;
				}
			}
		}
		return false;
	}

	private void RepairBuilding(int repairAmount)
	{
		if (_currentHealth >= MAX_HEALTH) return;

		_currentHealth = Math.Min(_currentHealth + repairAmount, MAX_HEALTH);
		UpdateHPBar();
		SaveBuildingState();

		GD.Print($"Palisade: Repaired for {repairAmount} HP. Current health: {_currentHealth}");
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
			CommonSignals.Instance.HammerUsed -= OnHammerUsed;
			CommonSignals.Instance.SwordUsed -= OnSwordUsed;
		}
	}

	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I buildingTile = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
				float distance = buildingTile.DistanceTo(playerTilePosition);
				if (distance <= 2.0f)
				{
					return true;
				}
			}
		}
		return false;
	}

	public Vector2I GetTilePosition()
	{
		return _topLeftTilePosition;
	}

	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	public void SetCurrentHealth(int health)
	{
		_currentHealth = health;
		UpdateHPBar();
	}

	public TargetType GetTargetType()
	{
		return TargetType.Wall;
	}

	public bool CanBeTargeted()
	{
		return _isPlaced && !_isBeingDestroyed && _currentHealth > 0;
	}

	public Vector2 GetTargetPosition()
	{
		return GlobalPosition;
	}

	public void OnTargeted(Node2D enemy)
	{
	}

	public void OnAttacked(int damage, EnemyType attackerType)
	{
		TakeDamage(damage);
	}
}
