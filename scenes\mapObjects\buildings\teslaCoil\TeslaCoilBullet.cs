using Godot;

public partial class TeslaCoilBullet : Area2D
{
	[Export] public float Speed = 200.0f;

	private Vector2 _direction;
	private Vector2 _startPosition;
	private float _maxDistance = 300.0f;
	private int _damage = 1;
	private AnimationPlayer _animationPlayer;
	private Sprite2D _sprite;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_startPosition = GlobalPosition;

		AreaEntered += OnAreaEntered;

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Animate"))
		{
			_animationPlayer.Play("Animate");
		}

		CollisionLayer = 0;
		CollisionMask = 64;
	}

	public void Initialize(Vector2 targetPosition, int damage, float maxDistance)
	{
		_direction = (targetPosition - GlobalPosition).Normalized();
		_damage = damage;
		_maxDistance = maxDistance;

		float angle = _direction.Angle();
		Rotation = angle;
	}

	public override void _PhysicsProcess(double delta)
	{
		Vector2 velocity = _direction * Speed;
		GlobalPosition += velocity * (float)delta;

		float traveledDistance = _startPosition.DistanceTo(GlobalPosition);

		if (traveledDistance >= _maxDistance)
		{
			QueueFree();
		}
	}

	private void OnAreaEntered(Area2D area)
	{
		var parent = area.GetParent();
		
		if (parent is BaseEnemy enemy)
		{
			enemy.TakeDamage(_damage);
			GD.Print($"TeslaCoilBullet: Hit {enemy.GetEnemyType()} for {_damage} damage!");
			QueueFree();
			return;
		}

		if (parent is Region10Boss boss)
		{
			boss.TakeDamage(_damage);
			GD.Print($"TeslaCoilBullet: Hit Region10Boss for {_damage} damage!");
			QueueFree();
			return;
		}

		if (parent is Region10SerpentFly serpent)
		{
			serpent.TakeDamage(_damage);
			GD.Print($"TeslaCoilBullet: Hit Region10SerpentFly for {_damage} damage!");
			QueueFree();
			return;
		}
	}
}
