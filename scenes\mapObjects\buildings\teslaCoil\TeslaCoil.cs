using Godot;
using System;
using System.Collections.Generic;

public partial class TeslaCoil : Node2D, IDestroyableObject, ICombatTarget
{
	[Export] public ObjectType BuildingType { get; set; } = ObjectType.TeslaCoil;
	[Export] public int MaxHealth { get; set; } = 30;
	[Export] public PackedScene TeslaCoilBulletScene { get; set; }

	private const int BUILDING_WIDTH = 1;
	private const int BUILDING_HEIGHT = 2;
	private const int TILE_SIZE = 16;
	private const float ATTACK_COOLDOWN = 3.0f;
	private const float MAX_BULLET_DISTANCE = 300.0f;

	public static readonly Dictionary<int, int> STONE_BAR_COSTS = new Dictionary<int, int>
	{
		{1, 10}, {2, 10}, {3, 10}, {4, 10}, {5, 10}, {6, 10}, {7, 10}, {8, 10}
	};

	public static readonly Dictionary<int, int> PLANK_COSTS = new Dictionary<int, int>
	{
		{1, 5}, {2, 5}, {3, 5}, {4, 5}, {5, 5}, {6, 5}, {7, 5}, {8, 5}
	};

	public static readonly Dictionary<int, ResourceType> UPGRADE_BAR_TYPES = new Dictionary<int, ResourceType>
	{
		{1, ResourceType.CopperBar}, {2, ResourceType.IronBar}, {3, ResourceType.GoldBar},
		{4, ResourceType.IndigosiumBar}, {5, ResourceType.MithrilBar}, {6, ResourceType.ErithrydiumBar},
		{7, ResourceType.AdamantiteBar}, {8, ResourceType.UraniumBar}
	};

	private int _currentHealth;
	private int _level = 1;
	private Vector2I _topLeftTilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isPlaced = false;
	private bool _isBeingDestroyed = false;
	private bool _isPlayerInRange = false;
	private string _saveId = "";

	private Sprite2D _sprite;
	private StaticBody2D _staticBody;
	private CollisionShape2D _collisionShape;
	private ProgressBar _hpBar;
	private AnimationPlayer _animationPlayer;
	private Area2D _playerDetector;
	private Area2D _enemyDetector;
	private Timer _attackTimer;

	private Color _normalColor = Colors.White;
	private Color _hitColor = new Color(1.0f, 0.42f, 0.27f, 1.0f);
	private Tween _hitTween;

	public override void _Ready()
	{
		_currentHealth = MaxHealth;

		_sprite = GetNode<Sprite2D>("Sprite2D");
		_staticBody = GetNode<StaticBody2D>("StaticBody2D");
		_collisionShape = GetNode<CollisionShape2D>("StaticBody2D/CollisionShape2D");
		_hpBar = GetNode<ProgressBar>("ProgressBar");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_playerDetector = GetNode<Area2D>("PlayerDetector");
		_enemyDetector = GetNode<Area2D>("EnemyDetector");

		if (_hpBar != null)
		{
			_hpBar.SetProgress(1.0f);
			_hpBar.Visible = false;
		}

		if (_playerDetector != null)
		{
			_playerDetector.CollisionMask = 4;
			_playerDetector.AreaEntered += OnPlayerEntered;
			_playerDetector.AreaExited += OnPlayerExited;
		}

		if (_enemyDetector != null)
		{
			_enemyDetector.CollisionMask = 8;
			_enemyDetector.BodyEntered += OnEnemyEntered;
			_enemyDetector.BodyExited += OnEnemyExited;
		}

		_attackTimer = new Timer();
		AddChild(_attackTimer);
		_attackTimer.WaitTime = ATTACK_COOLDOWN;
		_attackTimer.Timeout += OnAttackTimerTimeout;

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
			CommonSignals.Instance.HammerUsed += OnHammerUsed;
			CommonSignals.Instance.SwordUsed += OnSwordUsed;
		}

		UpdateHPBar();
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange || !_isPlaced) return;

		if (@event.IsActionPressed("Interact"))
		{
			OpenTeslaCoilMenu();
		}
	}

	private void OnPlayerEntered(Area2D body)
	{
		if (body.Name == "PlayerDetector")
		{
			_isPlayerInRange = true;
			CommonSignals.Instance?.EmitShowKeyEPrompt();
		}
	}

	private void OnPlayerExited(Area2D body)
	{
		if (body.Name == "PlayerDetector")
		{
			_isPlayerInRange = false;
			CommonSignals.Instance?.EmitHideKeyEPrompt();
		}
	}

	private void OnEnemyEntered(Node2D body)
	{
		if (body.IsInGroup("enemy") && _isPlaced && !_attackTimer.IsStopped())
		{
			_attackTimer.Start();
		}
	}

	private void OnEnemyExited(Node2D body)
	{
	}

	private void OnAttackTimerTimeout()
	{
		if (!_isPlaced) return;

		var closestEnemy = FindClosestEnemy();
		if (closestEnemy != null)
		{
			SpawnBullet(closestEnemy);
			_attackTimer.Start();
		}
	}

	private Node2D FindClosestEnemy()
	{
		Node2D closestEnemy = null;
		float closestDistance = float.MaxValue;

		var bodies = _enemyDetector.GetOverlappingBodies();
		foreach (Node2D body in bodies)
		{
			if (body.IsInGroup("enemy"))
			{
				float distance = GlobalPosition.DistanceTo(body.GlobalPosition);
				if (distance < closestDistance)
				{
					closestDistance = distance;
					closestEnemy = body;
				}
			}
		}

		return closestEnemy;
	}

	private void SpawnBullet(Node2D target)
	{
		if (TeslaCoilBulletScene == null) return;

		var bullet = TeslaCoilBulletScene.Instantiate<TeslaCoilBullet>();
		if (bullet == null) return;

		bullet.GlobalPosition = GlobalPosition;
		bullet.Initialize(target.GlobalPosition, _level, MAX_BULLET_DISTANCE);

		GetTree().CurrentScene.AddChild(bullet);
	}

	private void OpenTeslaCoilMenu()
	{
		var teslaCoilMenu = GetNode<TeslaCoilMenu>("TeslaCoilMenu");
		if (teslaCoilMenu != null)
		{
			teslaCoilMenu.SetTeslaCoil(this);

			if (MenuManager.Instance != null)
			{
				MenuManager.Instance.OpenMenu("TeslaCoilMenu");
			}
			else
			{
				teslaCoilMenu.OpenMenu(this);
			}
		}
	}

	public void PlaceBuilding()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		if (!resourcesManager.HasResource(ResourceType.IronBar, 10) ||
			!resourcesManager.HasResource(ResourceType.Plank, 5))
		{
			return;
		}

		if (!resourcesManager.RemoveResource(ResourceType.IronBar, 10) ||
			!resourcesManager.RemoveResource(ResourceType.Plank, 5))
		{
			return;
		}

		PlaceBuilding(_topLeftTilePosition, _customDataManager);
	}

	public void PlaceBuilding(Vector2I topLeftPosition, CustomDataLayerManager customDataManager)
	{
		_topLeftTilePosition = topLeftPosition;
		_customDataManager = customDataManager;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = new Vector2I(topLeftPosition.X + x, topLeftPosition.Y + y);
				customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}

		SetTilePosition(topLeftPosition);
		_isPlaced = true;

		_saveId = ResourcesManager.Instance?.AddBuilding(_topLeftTilePosition, "TeslaCoil", (int)BuildingType);
		SaveBuildingState();
	}

	public void LoadFromSave(Vector2I topLeftPosition, CustomDataLayerManager customDataManager, int health, int level = 1)
	{
		_topLeftTilePosition = topLeftPosition;
		_customDataManager = customDataManager;
		_currentHealth = health;
		_level = level;
		_isPlaced = true;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = new Vector2I(topLeftPosition.X + x, topLeftPosition.Y + y);
				customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}

		SetTilePosition(topLeftPosition);
		UpdateHPBar();
	}

	public void SetSaveId(string saveId)
	{
		_saveId = saveId;
	}

	public Vector2I GetTopLeftTilePosition()
	{
		return _topLeftTilePosition;
	}

	public int GetLevel()
	{
		return _level;
	}

	public void SetLevel(int level)
	{
		_level = level;
		SaveBuildingState();
	}

	public int GetDamage()
	{
		return _level;
	}

	public int GetNextLevelDamage()
	{
		return _level + 1;
	}

	public bool CanUpgrade()
	{
		return _level < 8;
	}

	public ResourceType GetUpgradeBarType()
	{
		if (!CanUpgrade()) return ResourceType.None;
		return UPGRADE_BAR_TYPES.GetValueOrDefault(_level + 1, ResourceType.None);
	}

	public int GetUpgradeBarCost()
	{
		if (!CanUpgrade()) return 0;
		return STONE_BAR_COSTS.GetValueOrDefault(_level + 1, 0);
	}

	public int GetUpgradePlankCost()
	{
		if (!CanUpgrade()) return 0;
		return PLANK_COSTS.GetValueOrDefault(_level + 1, 0);
	}

	public bool TryUpgrade()
	{
		if (!CanUpgrade()) return false;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		var barType = GetUpgradeBarType();
		int barCost = GetUpgradeBarCost();
		int plankCost = GetUpgradePlankCost();

		if (!resourcesManager.HasResource(barType, barCost) ||
			!resourcesManager.HasResource(ResourceType.Plank, plankCost))
		{
			return false;
		}

		if (!resourcesManager.RemoveResource(barType, barCost) ||
			!resourcesManager.RemoveResource(ResourceType.Plank, plankCost))
		{
			return false;
		}

		_level++;
		SaveBuildingState();
		return true;
	}

	public void SetTilePosition(Vector2I topLeftTile)
	{
		_topLeftTilePosition = topLeftTile;
		Vector2 worldPosition = new Vector2(
			topLeftTile.X * TILE_SIZE + (BUILDING_WIDTH * TILE_SIZE) / 2.0f,
			topLeftTile.Y * TILE_SIZE + (BUILDING_HEIGHT * TILE_SIZE) / 2.0f
		);
		GlobalPosition = worldPosition;
	}

	public bool CanBePlacedAt(Vector2I topLeftPosition)
	{
		if (_customDataManager == null)
		{
			_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		}

		if (_customDataManager == null) return false;

		// Check if all tiles in the 1x2 area are available for building
		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = topLeftPosition + new Vector2I(x, y);
				var tileData = _customDataManager.GetTileData(tilePos);

				if (!tileData.CanBuilding || tileData.ObjectTypePlaced != ObjectTypePlaced.None)
				{
					return false;
				}
			}
		}

		return true;
	}

	public void SetPlacementFeedback(bool canPlace)
	{
		if (_sprite != null)
		{
			_sprite.Modulate = canPlace ? Colors.White : new Color(1.0f, 0.5f, 0.5f, 0.8f);
		}
	}

	private void SaveBuildingState()
	{
		if (string.IsNullOrEmpty(_saveId)) return;

		var buildingData = GameSaveData.Instance.WorldData.Buildings.Find(b => b.Id == _saveId);
		if (buildingData != null)
		{
			buildingData.CurrentHealth = _currentHealth;
			buildingData.SelectedCraftingResource = _level;
		}
	}

	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;
		UpdateHPBar();
		SaveBuildingState();
		PlayHitAnimation();

		if (_animationPlayer != null && _animationPlayer.HasAnimation("hit"))
		{
			_animationPlayer.Play("hit");
		}

		if (_currentHealth <= 0)
		{
			DestroyBuilding();
		}
	}

	private void PlayHitAnimation()
	{
		if (_sprite == null) return;

		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.SetParallel(true);

		var hitColor = _normalColor.Lerp(_hitColor, 0.8f);
		_hitTween.TweenProperty(_sprite, "modulate", hitColor, 0.02f);
		_hitTween.TweenProperty(_sprite, "modulate", _normalColor, 0.18f)
				.SetDelay(0.02f);
	}

	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		float healthPercentage = (float)_currentHealth / MaxHealth;
		_hpBar.SetProgress(healthPercentage);

		if (_currentHealth < MaxHealth)
		{
			_hpBar.Visible = true;
		}
		else
		{
			_hpBar.Visible = false;
		}
	}

	public void DestroyBuilding()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
				_customDataManager?.ClearObjectPlaced(tilePos);
			}
		}

		if (!string.IsNullOrEmpty(_saveId))
		{
			var buildingData = GameSaveData.Instance.WorldData.Buildings.Find(b => b.Id == _saveId);
			if (buildingData != null)
			{
				GameSaveData.Instance.WorldData.Buildings.Remove(buildingData);
			}
		}

		QueueFree();
	}

	public bool IsPlaced()
	{
		return _isPlaced;
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I buildingTile = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
				if (buildingTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeHitFrom(playerTile, tilePosition))
					{
						TakeDamage(damage);
					}
					return;
				}
			}
		}
	}

	private void OnHammerUsed(Vector2I tilePosition, int repairAmount)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I buildingTile = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
				if (buildingTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeHitFrom(playerTile, tilePosition))
					{
						_currentHealth = Math.Min(_currentHealth + repairAmount, MaxHealth);
						UpdateHPBar();
						SaveBuildingState();
					}
					return;
				}
			}
		}
	}

	private void OnSwordUsed(Vector2I tilePosition, Vector2 playerPosition, Vector2 attackDirection)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I buildingTile = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
				Vector2 buildingWorldPos = new Vector2(buildingTile.X * TILE_SIZE + TILE_SIZE / 2, buildingTile.Y * TILE_SIZE + TILE_SIZE / 2);

				if (IsInSwordAttackArc(playerPosition, attackDirection, buildingWorldPos))
				{
					TakeDamage(2);
					return;
				}
			}
		}
	}

	private bool IsInSwordAttackArc(Vector2 playerPosition, Vector2 attackDirection, Vector2 targetPosition)
	{
		float distance = playerPosition.DistanceTo(targetPosition);
		if (distance > 32.0f) return false;

		Vector2 directionToTarget = (targetPosition - playerPosition).Normalized();
		float dotProduct = attackDirection.Dot(directionToTarget);

		float arcRadians = 180.0f * Mathf.Pi / 180.0f;
		float threshold = Mathf.Cos(arcRadians / 2.0f);

		return dotProduct >= threshold;
	}

	private bool CanBeHitFrom(Vector2I playerTilePosition, Vector2I targetTilePosition)
	{
		float distance = playerTilePosition.DistanceTo(targetTilePosition);
		return distance <= 2.0f;
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			return new Vector2I(
				Mathf.FloorToInt(player.GlobalPosition.X / TILE_SIZE),
				Mathf.FloorToInt(player.GlobalPosition.Y / TILE_SIZE)
			);
		}
		return Vector2I.Zero;
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
			CommonSignals.Instance.HammerUsed -= OnHammerUsed;
			CommonSignals.Instance.SwordUsed -= OnSwordUsed;
		}
	}

	public TargetType GetTargetType()
	{
		return TargetType.CombatBuilding;
	}

	public bool CanBeTargeted()
	{
		return _isPlaced && !_isBeingDestroyed && _currentHealth > 0;
	}

	public Vector2 GetTargetPosition()
	{
		return GlobalPosition;
	}

	public void OnTargeted(Node2D enemy)
	{
	}

	public void OnAttacked(int damage, EnemyType attackerType)
	{
		TakeDamage(damage);
	}

	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		float distance = playerTilePosition.DistanceTo(_topLeftTilePosition);
		return distance <= 2.0f;
	}

	public Vector2I GetTilePosition()
	{
		return _topLeftTilePosition;
	}

	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	public void SetCurrentHealth(int health)
	{
		_currentHealth = health;
		UpdateHPBar();
		SaveBuildingState();
	}
}
