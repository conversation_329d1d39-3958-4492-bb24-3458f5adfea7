[gd_scene load_steps=7 format=3 uid="uid://bxkpne5xwk63x"]

[ext_resource type="Texture2D" uid="uid://cgrhf8qbb2px7" path="res://resources/ELV_itchio/Light Spells/Lightning_Bolt02.png" id="1_inarx"]
[ext_resource type="Script" uid="uid://cemdf4y4yeo1f" path="res://scenes/mapObjects/buildings/teslaCoil/TeslaCoilBullet.cs" id="1_teslacoilbullet_script"]

[sub_resource type="Animation" id="Animation_mx77w"]
resource_name = "Animate"
length = 1.2
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
}

[sub_resource type="Animation" id="Animation_inarx"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_h8u6w"]
_data = {
&"Animate": SubResource("Animation_mx77w"),
&"RESET": SubResource("Animation_inarx")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_inarx"]
size = Vector2(20, 4)

[node name="TeslaCoilBullet" type="Area2D"]
collision_layer = 0
collision_mask = 64
script = ExtResource("1_teslacoilbullet_script")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("1_inarx")
hframes = 6
vframes = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_h8u6w")
}

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_inarx")
